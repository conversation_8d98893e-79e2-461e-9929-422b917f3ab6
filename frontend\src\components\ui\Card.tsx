import { HTMLAttributes, forwardRef } from 'react';
import { clsx } from 'clsx';

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'cost' | 'performance' | 'cache' | 'savings';
}

export const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', children, ...props }, ref) => {
    const variants = {
      default: 'border border-pv-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800',
      cost: 'bg-gradient-to-br from-pv-yellow-50 to-pv-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-l-4 border-pv-yellow-500 dark:border-yellow-400',
      performance: 'bg-gradient-to-br from-pv-cyan-50 to-pv-cyan-100 dark:from-cyan-900/20 dark:to-cyan-800/20 border-l-4 border-pv-cyan-500 dark:border-cyan-400',
      cache: 'bg-gradient-to-br from-pv-blue-50 to-pv-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-l-4 border-pv-blue-primary dark:border-blue-400',
      savings: 'bg-gradient-to-br from-pv-orange-50 to-pv-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-l-4 border-pv-orange-500 dark:border-orange-400'
    };

    return (
      <div
        ref={ref}
        className={clsx(
          'rounded-lg p-3 sm:p-4 lg:p-5 xl:p-4 2xl:p-5 shadow-sm transition-all duration-200 hover:shadow-md',
          variants[variant],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

export const CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={clsx('flex flex-col space-y-1.5 pb-2 sm:pb-3 lg:pb-4', className)}
      {...props}
    />
  )
);

CardHeader.displayName = 'CardHeader';

export const CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={clsx('text-lg font-semibold leading-none tracking-tight text-pv-gray-800 dark:text-white', className)}
      {...props}
    />
  )
);

CardTitle.displayName = 'CardTitle';

export const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={clsx('', className)} {...props} />
  )
);

CardContent.displayName = 'CardContent';