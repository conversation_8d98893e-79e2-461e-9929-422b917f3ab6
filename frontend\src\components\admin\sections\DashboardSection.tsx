'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui';
import { FaChartLine, FaDatabase, FaRobot, FaUsers } from 'react-icons/fa';

interface DashboardSectionProps {
  metrics?: any;
  postgresMetrics?: any;
  postgresConnected?: boolean;
}

export function DashboardSection({ 
  metrics, 
  postgresMetrics, 
  postgresConnected 
}: DashboardSectionProps) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Dashboard Administrativo
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Visão geral do sistema e métricas principais
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${postgresConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm text-gray-600 dark:text-gray-300">
            {postgresConnected ? 'Todos os sistemas online' : 'Problemas de conectividade'}
          </span>
        </div>
      </div>

      {/* Cards de Resumo */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6 gap-2 sm:gap-3 lg:gap-4 xl:gap-2 2xl:gap-3 3xl:gap-4">
        {/* Custo Diário */}
        <Card variant="cost" className="relative overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-900 dark:text-white">Custo Hoje</CardTitle>
              <div className="p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0 2.08-.402 2.599-1" />
                </svg>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {metrics?.realTimeCost?.dailySpent || '$0.00'}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {metrics?.budget?.daily ? `De um orçamento de ${metrics.budget.daily}` : 'Orçamento não definido'}
            </p>
          </CardContent>
        </Card>

        {/* Protocolos Ativos */}
        <Card variant="performance" className="relative overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-900 dark:text-white">Protocolos Ativos</CardTitle>
              <div className="p-2 rounded-lg bg-cyan-100 dark:bg-cyan-900/30 text-cyan-600 dark:text-cyan-400">
                <FaDatabase className="w-5 h-5" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {postgresMetrics?.totalProtocolos?.toLocaleString('pt-BR') || '0'}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Total no sistema
            </p>
          </CardContent>
        </Card>

        {/* Mensagens Hoje */}
        <Card variant="cache" className="relative overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-900 dark:text-white">Mensagens Hoje</CardTitle>
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                <FaRobot className="w-5 h-5" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {metrics?.usage?.totalMessages || '0'}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Interações com IA
            </p>
          </CardContent>
        </Card>

        {/* Tokens Ativos */}
        <Card variant="savings" className="relative overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-900 dark:text-white">Tokens Ativos</CardTitle>
              <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400">
                <FaUsers className="w-5 h-5" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {metrics?.activeTokens || '0'}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Usuários autorizados
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Gráficos e Informações Adicionais */}
      <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4 gap-3 lg:gap-4 xl:gap-2 2xl:gap-3 3xl:gap-4">
        {/* Status dos Serviços */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-gray-900 dark:text-white">
              <FaChartLine className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <span>Status dos Serviços</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">API Principal</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-600 dark:text-green-400">Online</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">PostgreSQL</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${postgresConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className={`text-sm ${postgresConnected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {postgresConnected ? 'Conectado' : 'Desconectado'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">IA (OpenAI)</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-600 dark:text-green-400">Operacional</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Atividade Recente */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-gray-900 dark:text-white">
              <FaDatabase className="w-5 h-5 text-green-600 dark:text-green-400" />
              <span>Atividade Recente</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics?.recentActivity?.length > 0 ? (
                metrics.recentActivity.map((activity: any, index: number) => (
                  <div key={index} className="text-sm">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {activity.description}
                    </div>
                    <div className="text-gray-600 dark:text-gray-300">
                      {activity.timestamp}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Nenhuma atividade recente
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
