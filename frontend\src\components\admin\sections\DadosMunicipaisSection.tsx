'use client';

import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle
} from '@/components/ui';
import { MetricCard } from '@/components/admin';
import { FaDatabase, FaFileAlt, FaBuilding, FaChartBar } from 'react-icons/fa';

interface DadosMunicipaisSectionProps {
  postgresMetrics?: any;
  protocolosRecentes?: any[];
  alvarasRecentes?: any[];
  topDepartamentos?: any[];
  isConnected?: boolean;
  loading?: boolean;
}

export function DadosMunicipaisSection({ 
  postgresMetrics, 
  protocolosRecentes, 
  alvarasRecentes, 
  topDepartamentos,
  isConnected,
  loading 
}: DadosMunicipaisSectionProps) {
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-4 lg:gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FaDatabase className="w-8 h-8 text-green-600 dark:text-green-400" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Dados Municipais
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Informações em tempo real do banco de dados PostgreSQL
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm text-gray-600 dark:text-gray-300">
            PostgreSQL {isConnected ? 'Conectado' : 'Desconectado'}
          </span>
        </div>
      </div>

      {/* Métricas Principais */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-4 lg:gap-6">
        <MetricCard
          title="Protocolos Ativos"
          value={postgresMetrics?.totalProtocolos?.toLocaleString('pt-BR') || '0'}
          subtitle="Total no sistema"
          variant="performance"
          trend={postgresMetrics?.protocolosTrend ? { value: postgresMetrics.protocolosTrend.value, isPositive: postgresMetrics.protocolosTrend.isPositive } : undefined}
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          }
        />

        <MetricCard
          title="Alvarás Emitidos"
          value={postgresMetrics?.totalAlvaras?.toLocaleString('pt-BR') || '0'}
          subtitle="Total de alvarás"
          variant="cache"
          trend={postgresMetrics?.alvarasTrend ? { value: postgresMetrics.alvarasTrend.value, isPositive: postgresMetrics.alvarasTrend.isPositive } : undefined}
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
            </svg>
          }
        />

        <MetricCard
          title="Departamentos"
          value={postgresMetrics?.totalDepartamentos?.toLocaleString('pt-BR') || '0'}
          subtitle="Secretarias ativas"
          variant="savings"
          trend={postgresMetrics?.departamentosTrend ? { value: postgresMetrics.departamentosTrend.value, isPositive: postgresMetrics.departamentosTrend.isPositive } : undefined}
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          }
        />

        <MetricCard
          title="Consultas/Min"
          value={postgresMetrics?.queryRate || '0'}
          subtitle="Taxa de consultas"
          variant="cost"
          trend={postgresMetrics?.queryRateTrend ? { value: postgresMetrics.queryRateTrend.value, isPositive: postgresMetrics.queryRateTrend.isPositive } : undefined}
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          }
        />
      </div>

      {/* Tabs com informações detalhadas */}
      <Tabs defaultValue="protocolos" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="protocolos" className="flex items-center space-x-2">
            <FaFileAlt className="w-4 h-4" />
            <span>Protocolos</span>
          </TabsTrigger>
          <TabsTrigger value="alvaras" className="flex items-center space-x-2">
            <FaBuilding className="w-4 h-4" />
            <span>Alvarás</span>
          </TabsTrigger>
          <TabsTrigger value="departamentos" className="flex items-center space-x-2">
            <FaChartBar className="w-4 h-4" />
            <span>Departamentos</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="protocolos" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Protocolos Recentes</CardTitle>
            </CardHeader>
            <CardContent>
              {protocolosRecentes && protocolosRecentes.length > 0 ? (
                <div className="space-y-3">
                  {protocolosRecentes.slice(0, 5).map((protocolo, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          Protocolo #{protocolo.numero || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {protocolo.tipo || 'Tipo não especificado'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {protocolo.status || 'Em andamento'}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {protocolo.data ? new Date(protocolo.data).toLocaleDateString('pt-BR') : 'Data não disponível'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  Nenhum protocolo recente encontrado
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alvaras" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Alvarás Recentes</CardTitle>
            </CardHeader>
            <CardContent>
              {alvarasRecentes && alvarasRecentes.length > 0 ? (
                <div className="space-y-3">
                  {alvarasRecentes.slice(0, 5).map((alvara, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          Alvará #{alvara.numero || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {alvara.tipo || 'Tipo não especificado'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-green-600 dark:text-green-400">
                          Emitido
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {alvara.data ? new Date(alvara.data).toLocaleDateString('pt-BR') : 'Data não disponível'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  Nenhum alvará recente encontrado
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="departamentos" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Departamentos Mais Ativos</CardTitle>
            </CardHeader>
            <CardContent>
              {topDepartamentos && topDepartamentos.length > 0 ? (
                <div className="space-y-3">
                  {topDepartamentos.slice(0, 5).map((dept, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {dept.nome || 'Departamento não especificado'}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {dept.descricao || 'Sem descrição'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          {dept.total_protocolos || '0'} protocolos
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Este mês
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  Nenhum departamento encontrado
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
